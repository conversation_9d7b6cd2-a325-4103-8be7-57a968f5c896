import random
import sys
import time
import threading
import json
from queue import Queue, Empty
from datetime import datetime, timedelta
from typing import Dict, List, Set

from spain_visa_appointment_date_open import book_appointment
from extension.logger import logger
from extension.session_manager import create_session
from tool import send_dd_msg, area_map, send_wx_post_days
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, publish_redis_msg, save_logs
from user_manager import set_appointment_info, spain_faker_scaning, get_user_info, spain_user_field
from config import default_centers

# 配置常量
REAL_USER_SCAN_INTERVAL = 3  # 有真实用户地区扫描间隔
REAL_USER_SUCCESS_PAUSE = 20  # 有真实用户地区成功后暂停
FAKE_USER_SCAN_INTERVAL = 15  # 无真实用户地区扫描间隔
FAKE_USER_SUCCESS_PAUSE = 30 * 60  # 无真实用户地区成功后暂停：30分钟
FAKE_USER_ROUND_PAUSE = 20 * 60  # 无真实用户地区每轮扫描完成后暂停：20分钟

user_queue = Queue()
user_queue_dict = {}


# 无真实用户地区的线程池管理器
class FakeUserThreadPool:
    def __init__(self, num_workers=2):
        self.num_workers = num_workers
        self.task_queue = Queue()
        self.workers = []
        self.running = True
        self.region_users = {}  # 存储每个地区的用户列表
        self.region_user_index = {}  # 存储每个地区当前使用的用户索引
        self.lock = threading.Lock()

        # 启动工作线程
        for i in range(num_workers):
            worker = threading.Thread(target=self._worker, daemon=True, args=(i,))
            worker.start()
            self.workers.append(worker)
            logger.info(f"#线程池# 启动无真实用户地区工作线程 {i}")

    def _worker(self, worker_id):
        """工作线程函数"""
        logger.debug(f"#线程池# 工作线程 {worker_id} 启动")

        while self.running:
            try:
                # 获取任务，超时5秒
                task = self.task_queue.get(timeout=5)
                if task is None:  # 停止信号
                    break

                region, scan_type = task
                logger.debug(f"#线程池# 工作线程 {worker_id} 处理任务: {region}-{scan_type}")

                # 执行扫描任务
                self._execute_scan_task(region, scan_type, worker_id)

                self.task_queue.task_done()

            except Empty:
                # 超时，继续循环
                continue
            except Exception as e:
                logger.error(f"#线程池# 工作线程 {worker_id} 异常: {e}")
                if 'task' in locals():
                    self.task_queue.task_done()

        logger.debug(f"#线程池# 工作线程 {worker_id} 结束")

    def _execute_scan_task(self, region, scan_type, worker_id):
        """执行扫描任务"""
        try:
            # 获取该地区的用户
            user = self._get_next_user(region, scan_type)
            if not user:
                logger.warning(f"#线程池# 地区 {region}-{scan_type} 没有可用用户")
                return

            # 标记开始扫描
            scan_status.start_scanning(region)

            # 执行扫描
            success, result = scan_job(user)

            # 更新扫描状态
            scan_status.finish_scanning(region, success)

            if success and result:  # 扫描成功且有开放日期
                logger.info(f"#线程池# 工作线程 {worker_id} 地区 {region}-{scan_type} 发现开放日期，暂停 {FAKE_USER_SUCCESS_PAUSE//60} 分钟")
                # 无真实用户地区成功后不再继续扫描该地区
                return
            else:
                # 扫描失败或无开放日期，等待后继续
                logger.debug(f"#线程池# 工作线程 {worker_id} 地区 {region}-{scan_type} 扫描完成，{FAKE_USER_SCAN_INTERVAL}秒后可重新扫描")
                time.sleep(FAKE_USER_SCAN_INTERVAL)

        except Exception as e:
            logger.error(f"#线程池# 执行扫描任务异常 {region}-{scan_type}: {e}")
            scan_status.finish_scanning(region, False)

    def _get_next_user(self, region, scan_type):
        """获取地区的下一个用户"""
        with self.lock:
            region_key = f"{region}-{scan_type}"

            # 如果该地区没有用户列表，返回None
            if region_key not in self.region_users:
                return None

            users = self.region_users[region_key]
            if not users:
                return None

            # 获取当前索引
            current_index = self.region_user_index.get(region_key, 0)
            user = users[current_index]

            # 更新索引，循环使用
            self.region_user_index[region_key] = (current_index + 1) % len(users)

            return user

    def add_region_users(self, region, scan_type, users):
        """为地区添加用户列表"""
        with self.lock:
            region_key = f"{region}-{scan_type}"
            self.region_users[region_key] = users
            self.region_user_index[region_key] = 0
            logger.debug(f"#线程池# 添加地区用户 {region_key}，用户数: {len(users)}")

    def add_scan_task(self, region, scan_type):
        """添加扫描任务"""
        task = (region, scan_type)
        self.task_queue.put(task)
        logger.debug(f"#线程池# 添加扫描任务: {region}-{scan_type}")

    def stop(self):
        """停止线程池"""
        self.running = False
        # 发送停止信号
        for _ in range(self.num_workers):
            self.task_queue.put(None)


# 全局线程池实例
fake_user_thread_pool = FakeUserThreadPool(num_workers=2)


# 扫描状态管理
class ScanStatus:
    def __init__(self):
        self.region_last_scan: Dict[str, float] = {}  # 地区最后扫描时间
        self.region_last_success: Dict[str, float] = {}  # 地区最后成功时间
        self.scanning_regions: Set[str] = set()  # 正在扫描的地区

    def should_scan_region(self, region: str, has_real_users: bool = False) -> bool:
        """判断地区是否应该扫描

        Args:
            region: 地区标识
            has_real_users: 是否有真实用户
        """
        current_time = time.time()

        # 如果正在扫描，跳过
        if region in self.scanning_regions:
            return False

        # 有真实用户的地区：只检查基本扫描间隔（1秒）
        if has_real_users:
            last_scan = self.region_last_scan.get(region, 0)
            if current_time - last_scan < REAL_USER_SCAN_INTERVAL:
                return False
            return True

        # 无真实用户的地区：检查成功后的暂停时间
        last_success = self.region_last_success.get(region, 0)
        if current_time - last_success < FAKE_USER_SUCCESS_PAUSE:
            return False

        # 检查正常扫描间隔
        last_scan = self.region_last_scan.get(region, 0)
        if current_time - last_scan < FAKE_USER_SCAN_INTERVAL:
            return False

        return True

    def start_scanning(self, region: str):
        """开始扫描地区"""
        self.scanning_regions.add(region)
        self.region_last_scan[region] = time.time()

    def finish_scanning(self, region: str, success: bool):
        """完成扫描地区"""
        self.scanning_regions.discard(region)
        if success:
            self.region_last_success[region] = time.time()

    def get_next_scan_time(self, region: str, has_real_users: bool = False) -> float:
        """获取下次扫描时间"""
        last_scan = self.region_last_scan.get(region, 0)
        last_success = self.region_last_success.get(region, 0)

        if has_real_users:
            # 有真实用户的地区，使用较短的扫描间隔
            return last_scan + REAL_USER_SCAN_INTERVAL
        else:
            # 无真实用户的地区
            # 如果最近成功过，使用成功暂停时间
            if last_success > last_scan:
                return last_success + FAKE_USER_SUCCESS_PAUSE
            # 否则使用正常扫描间隔
            return last_scan + FAKE_USER_SCAN_INTERVAL


# 全局扫描状态
scan_status = ScanStatus()


def scan_job(user):
    """原始扫描任务"""
    # 登录状态为否 跳过
    if not user:
        return False, None
    user = get_user_info(user)
    if not user:
        return False, None
    if not user.get("is_login", False):
        return False, None

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    session = create_session(proxy, cookie_dict)

    # 查询预约号
    user["dateVIP"] = True if user.get("acceptVIP", 2) == 1 else False
    # 查询预约号 book_appointment 内部会更新 dateVIP 字段 表示是否真的扫了VIP号
    flag_book, res_book = book_appointment(user, session)

    visaType = user.get("visaTypeCode")
    area = user.get("centerCode")
    isVIP = user.get("dateVIP", False)
    # 取最新的用户信息
    user = get_user_info(user)
    user["is_login"] = False
    user["dateVIP"] = isVIP
    save_user_2_redis_queue(user)

    if not flag_book:  # 查询放号流程出错了
        logger.error(f"#放号查询失败# 西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 可约: False, {res_book}")
        return False, None

    success = res_book.get("success")
    if not success:  # 未开放
        logger.info(f"#放号查询结果# 西班牙: {area_map.get(area.upper(), '')} {visaType} {'VIP' if isVIP else 'Normal'} 可约: {success}, {res_book.get('message', '')}")
        info_dict = {"centerCode": area, "dates": [], "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
        save_logs(user, info_dict)
        return True, []  # 查询成功但无开放日期
    else:  # 扫号有开放日期，发送通知
        open_days = res_book.get("days", [])
        all_days_str = " | ".join(date_text[5:] for date_text in open_days)
        tip_str = f"西班牙: {area_map.get(area.upper(), area)} {visaType} {'VIP' if isVIP else 'Normal'} 放号。可约日期: {all_days_str}"
        logger.success("#放号查询结果# " + tip_str + " - " + user.get("email"))

        info_dict = {"centerCode": area, "dates": open_days, "isVIP": isVIP, "visaType": visaType, "country": "spain", "updateTime": time.time()}
        save_logs(user, info_dict)

        if len(open_days) == 0:  # 如果开放日期不存在，就继续扫号，不暂停
            return True, []

        # 发送redis消息，告知订阅者可以开始预约了
        publish_redis_msg(json.dumps(info_dict, ensure_ascii=False))
        app_field = f"spain-{area}-{visaType}-{isVIP}"
        set_appointment_info(app_field, json.dumps(info_dict, ensure_ascii=False))

        # 发送微信 钉钉消息提醒
        send_dd_msg("#放号查询# " + tip_str, area + visaType + str(isVIP))
        send_wx_post_days("#放号查询# " + tip_str, area + visaType + str(isVIP))

        return True, open_days


def thread_worker(center_visaType=None, has_real_users=False):
    """线程工作函数 - 实现队列循环使用和差异化扫描策略

    Args:
        center_visaType: 地区-签证类型-用户类型
        has_real_users: 该地区是否有真实用户
    """
    task_queue = user_queue_dict.get(center_visaType)
    region = center_visaType.replace("-vip", "").replace("-normal", "")
    used_users = []  # 记录本轮已使用的用户

    logger.debug(f"#线程启动# 地区 {region} ({'有真实用户' if has_real_users else '无真实用户'}) 工作线程启动")

    while True:  # 持续运行
        # 检查队列是否为空
        if task_queue.empty():
            # 队列为空时，将已使用的用户重新放回队列
            if used_users:
                logger.debug(f"#队列重置# 地区 {region} 重新填充队列，用户数: {len(used_users)}")
                for user in used_users:
                    task_queue.put(user)
                used_users.clear()

            # 如果仍然为空，等待一段时间后继续检查
            if task_queue.empty():
                logger.debug(f"#队列等待# 地区 {region} 队列为空，等待5秒")
                time.sleep(5)
                continue

        # 从队列获取用户
        user = task_queue.get()
        used_users.append(user)  # 记录已使用的用户

        # 标记开始扫描
        scan_status.start_scanning(region)

        try:
            # 直接执行扫描任务，每次都使用队列中的新用户
            success, result = scan_job(user)

            # 更新扫描状态
            scan_status.finish_scanning(region, success)

            if success:  # 扫描成功且有开放日期
                if has_real_users:
                    logger.debug(f"#扫描成功# 地区 {region} (有真实用户) 发现开放日期，{REAL_USER_SUCCESS_PAUSE}秒后继续扫描")
                    time.sleep(REAL_USER_SUCCESS_PAUSE)  # 成功后间隔10秒
                    # 继续使用下一个用户
                else:
                    logger.info(f"#扫描成功# 地区 {region} (无真实用户) 发现开放日期，暂停 {FAKE_USER_SUCCESS_PAUSE//60} 分钟")
                    # 无真实用户的地区暂停扫描，退出循环
                    break
            else:
                # 扫描失败或无开放日期，立即使用下一个用户
                if has_real_users:
                    logger.debug(f"#继续扫描# 地区 {region} (有真实用户) 扫描失败，{REAL_USER_SCAN_INTERVAL}秒后使用下一个用户")
                    time.sleep(REAL_USER_SCAN_INTERVAL)  # 1秒间隔
                else:
                    logger.debug(f"#继续扫描# 地区 {region} (无真实用户) 扫描失败，{FAKE_USER_SCAN_INTERVAL}秒后使用下一个用户")
                    time.sleep(FAKE_USER_SCAN_INTERVAL)  # 1秒间隔

        except Exception as e:
            logger.error(f"#线程异常# 地区 {region} 线程工作异常: {e}")
            scan_status.finish_scanning(region, False)
            # 异常时也继续下一个用户，不延迟

        task_queue.task_done()

    # 线程结束前，将剩余用户放回队列（仅无真实用户地区会到达这里）
    if used_users:
        logger.debug(f"#线程结束# 地区 {region} 将剩余用户放回队列，用户数: {len(used_users)}")
        for user in used_users:
            task_queue.put(user)

    logger.debug(f"#线程结束# 地区 {region} 工作线程结束")


def get_regions_info() -> tuple[List[str], Set[str]]:
    """获取所有需要扫描的地区信息，返回(所有地区, 有真实用户的地区)"""
    all_regions = set()
    real_user_regions = set()

    # 获取有真实用户的地区
    spain_users = get_users_with_queue_name(spain_user_field)
    for user in spain_users:
        center_tag = f"{user['centerCode']}-{user['visaTypeCode']}"
        real_user_regions.add(center_tag)
        all_regions.add(center_tag)

    # 获取所有虚拟用户地区（确保无真实用户的地区也被扫描）
    all_faker_users = get_users_with_queue_name(spain_faker_scaning)
    for user in all_faker_users:
        center_tag = f"{user['centerCode']}-{user['visaTypeCode']}"
        # all_regions.add(center_tag)

    # 如果没有任何用户，使用默认地区
    if not all_regions:
        for center in default_centers:
            all_regions.add(f"{center}-Tourism")
            all_regions.add(f"{center}-Business")

    return list(all_regions), real_user_regions, all_faker_users


def start_region_scanning(region: str, has_real_users: bool = False, all_faker_users=[]):
    """启动地区扫描

    Args:
        region: 地区标识
        has_real_users: 该地区是否有真实用户
    """
    center_code, visa_type = region.split("-")

    # 获取该地区的虚拟用户
    users = [u for u in all_faker_users if u.get("centerCode") == center_code and u.get("visaTypeCode") == visa_type]

    if not users:
        logger.warning(f"#扫描警告# 地区 {region} 没有可用的虚拟用户")
        return

    # 分离VIP和普通用户
    vip_users = [u for u in users if u.get("acceptVIP") == 1]
    normal_users = [u for u in users if u.get("acceptVIP") != 1]

    user_type_label = "有真实用户" if has_real_users else "无真实用户"

    if has_real_users:
        # 有真实用户的地区：使用独立线程（保持原有逻辑）
        if vip_users:
            vip_queue = Queue()
            for user in vip_users:
                vip_queue.put(user)
            vip_key = f"{region}-vip"
            user_queue_dict[vip_key] = vip_queue
            threading.Thread(target=thread_worker, daemon=True, args=(vip_key, has_real_users)).start()
            logger.info(f"#启动扫描# 地区 {region}-VIP队列 ({user_type_label})，用户数: {len(vip_users)}")

        if normal_users:
            normal_queue = Queue()
            for user in normal_users:
                normal_queue.put(user)
            normal_key = f"{region}-normal"
            user_queue_dict[normal_key] = normal_queue
            threading.Thread(target=thread_worker, daemon=True, args=(normal_key, has_real_users)).start()
            logger.info(f"#启动扫描# 地区 {region}-Normal队列 ({user_type_label})，用户数: {len(normal_users)}")
    else:
        # 无真实用户的地区：使用线程池
        if vip_users:
            fake_user_thread_pool.add_region_users(region, "vip", vip_users)
            fake_user_thread_pool.add_scan_task(region, "vip")
            logger.info(f"#线程池扫描# 地区 {region}-VIP ({user_type_label})，用户数: {len(vip_users)}")

        if normal_users:
            fake_user_thread_pool.add_region_users(region, "normal", normal_users)
            fake_user_thread_pool.add_scan_task(region, "normal")
            logger.info(f"#线程池扫描# 地区 {region}-Normal ({user_type_label})，用户数: {len(normal_users)}")


def start_scaning():
    """主扫描控制函数"""
    logger.info("#扫号#用户扫号启动")
    logger.info("- 有真实用户的地区：持续扫描，间隔3秒，成功后暂停20秒")
    logger.info("- 无真实用户的地区：使用2个线程池扫描，间隔15秒，成功后暂停30分钟")

    # 记录已启动的无真实用户地区，避免重复添加到线程池
    fake_regions_started = set()

    while True:
        try:
            # 获取所有需要扫描的地区信息
            all_regions, real_user_regions, all_faker_users = get_regions_info()

            if not all_regions:
                logger.warning("#扫描警告# 没有找到任何地区，等待中...")
                time.sleep(60)
                continue

            # 检查每个地区是否需要扫描
            for region in all_regions:
                has_real_users = region in real_user_regions

                if has_real_users:
                    # 有真实用户的地区：使用原有逻辑
                    if scan_status.should_scan_region(region, has_real_users):
                        region_type = "有真实用户"
                        logger.debug(f"#开始扫描# 地区 {region} ({region_type})")
                        start_region_scanning(region, has_real_users, all_faker_users)
                    else:
                        # 计算下次扫描时间
                        next_scan_time = scan_status.get_next_scan_time(region, has_real_users)
                        remaining_time = next_scan_time - time.time()
                        if remaining_time > 0:
                            if remaining_time >= 60:
                                logger.debug(f"#等待扫描# 地区 {region} (有真实用户) 还需等待 {remaining_time//60:.1f} 分钟")
                            else:
                                logger.debug(f"#等待扫描# 地区 {region} (有真实用户) 还需等待 {remaining_time:.1f} 秒")
                else:
                    # 无真实用户的地区：添加到线程池（只添加一次）
                    if region not in fake_regions_started:
                        logger.debug(f"#线程池初始化# 地区 {region} (无真实用户)")
                        start_region_scanning(region, has_real_users, all_faker_users)
                        fake_regions_started.add(region)
                    else:
                        # 检查是否需要重新扫描（成功暂停时间过后）
                        if scan_status.should_scan_region(region, has_real_users):
                            logger.debug(f"#线程池重新扫描# 地区 {region} (无真实用户)")
                            # 重新添加扫描任务到线程池
                            center_code, visa_type = region.split("-")
                            users = [u for u in all_faker_users if u.get("centerCode") == center_code and u.get("visaTypeCode") == visa_type]

                            if users:
                                vip_users = [u for u in users if u.get("acceptVIP") == 1]
                                normal_users = [u for u in users if u.get("acceptVIP") != 1]

                                if vip_users:
                                    fake_user_thread_pool.add_scan_task(region, "vip")
                                if normal_users:
                                    fake_user_thread_pool.add_scan_task(region, "normal")

            # 主循环休眠
            time.sleep(5)  # 每5秒检查一次

        except Exception as e:
            logger.error(f"#扫描异常# 主循环异常: {e}")
            time.sleep(5)


if __name__ == "__main__":
    # 获取外部参数
    start_scaning()
