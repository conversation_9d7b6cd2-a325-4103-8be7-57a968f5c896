#!/usr/bin/env python3
"""
测试新的线程分配策略
验证无真实用户地区使用线程池，有真实用户地区使用独立线程
"""

import time
import threading
from unittest.mock import Mock, patch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟依赖
class MockLogger:
    def info(self, msg): print(f"[INFO] {msg}")
    def debug(self, msg): print(f"[DEBUG] {msg}")
    def warning(self, msg): print(f"[WARNING] {msg}")
    def error(self, msg): print(f"[ERROR] {msg}")
    def success(self, msg): print(f"[SUCCESS] {msg}")

# 模拟用户数据
mock_real_users = [
    {"centerCode": "BEIJING", "visaTypeCode": "Tourism", "acceptVIP": 1, "email": "<EMAIL>"},
    {"centerCode": "BEIJING", "visaTypeCode": "Tourism", "acceptVIP": 2, "email": "<EMAIL>"},
]

mock_fake_users = [
    {"centerCode": "SHANGHAI", "visaTypeCode": "Tourism", "acceptVIP": 1, "email": "<EMAIL>"},
    {"centerCode": "SHANGHAI", "visaTypeCode": "Tourism", "acceptVIP": 2, "email": "<EMAIL>"},
    {"centerCode": "GUANGZHOU", "visaTypeCode": "Business", "acceptVIP": 1, "email": "<EMAIL>"},
    {"centerCode": "GUANGZHOU", "visaTypeCode": "Business", "acceptVIP": 2, "email": "<EMAIL>"},
]

def mock_scan_job(user):
    """模拟扫描任务"""
    print(f"  扫描用户: {user['email']} ({user['centerCode']}-{user['visaTypeCode']})")
    time.sleep(0.1)  # 模拟扫描时间
    return True, []  # 模拟扫描成功但无开放日期

def mock_get_users_with_queue_name(queue_name):
    """模拟获取用户数据"""
    if queue_name == "spainUserDatas":  # 真实用户
        return mock_real_users
    elif queue_name == "spain_users_scaning":  # 虚拟用户
        return mock_fake_users
    return []

def test_thread_allocation():
    """测试线程分配策略"""
    print("=" * 60)
    print("测试新的线程分配策略")
    print("=" * 60)
    
    # 模拟导入和替换
    with patch('main_faker_scan_optimeized.logger', MockLogger()):
        with patch('main_faker_scan_optimeized.scan_job', mock_scan_job):
            with patch('main_faker_scan_optimeized.get_users_with_queue_name', mock_get_users_with_queue_name):
                
                # 导入优化后的模块
                from main_faker_scan_optimeized import get_regions_info, start_region_scanning, fake_user_thread_pool
                
                print("\n1. 获取地区信息...")
                all_regions, real_user_regions, all_faker_users = get_regions_info()
                
                print(f"所有地区: {all_regions}")
                print(f"有真实用户的地区: {real_user_regions}")
                print(f"虚拟用户数量: {len(all_faker_users)}")
                
                print("\n2. 测试有真实用户地区的线程分配...")
                for region in real_user_regions:
                    print(f"启动有真实用户地区扫描: {region}")
                    start_region_scanning(region, has_real_users=True, all_faker_users=all_faker_users)
                
                print("\n3. 测试无真实用户地区的线程池分配...")
                fake_regions = [r for r in all_regions if r not in real_user_regions]
                for region in fake_regions:
                    print(f"启动无真实用户地区扫描: {region}")
                    start_region_scanning(region, has_real_users=False, all_faker_users=all_faker_users)
                
                print("\n4. 等待线程池处理任务...")
                time.sleep(3)  # 等待线程池处理任务
                
                print("\n5. 检查线程状态...")
                print(f"线程池工作线程数: {fake_user_thread_pool.num_workers}")
                print(f"线程池任务队列大小: {fake_user_thread_pool.task_queue.qsize()}")
                
                # 检查活跃线程数
                active_threads = threading.active_count()
                print(f"当前活跃线程数: {active_threads}")
                
                print("\n测试完成！")
                print("=" * 60)

def test_load_balancing():
    """测试负载均衡"""
    print("\n测试负载均衡...")

    with patch('main_faker_scan_optimeized.logger', MockLogger()):
        with patch('main_faker_scan_optimeized.scan_job', mock_scan_job):

            from main_faker_scan_optimeized import fake_user_thread_pool

            # 先添加用户数据到线程池
            test_regions = ["SHANGHAI-Tourism", "GUANGZHOU-Business"]

            # 为每个地区添加用户数据
            shanghai_vip_users = [u for u in mock_fake_users if u['centerCode'] == 'SHANGHAI' and u['acceptVIP'] == 1]
            shanghai_normal_users = [u for u in mock_fake_users if u['centerCode'] == 'SHANGHAI' and u['acceptVIP'] == 2]
            guangzhou_vip_users = [u for u in mock_fake_users if u['centerCode'] == 'GUANGZHOU' and u['acceptVIP'] == 1]
            guangzhou_normal_users = [u for u in mock_fake_users if u['centerCode'] == 'GUANGZHOU' and u['acceptVIP'] == 2]

            fake_user_thread_pool.add_region_users("SHANGHAI-Tourism", "vip", shanghai_vip_users)
            fake_user_thread_pool.add_region_users("SHANGHAI-Tourism", "normal", shanghai_normal_users)
            fake_user_thread_pool.add_region_users("GUANGZHOU-Business", "vip", guangzhou_vip_users)
            fake_user_thread_pool.add_region_users("GUANGZHOU-Business", "normal", guangzhou_normal_users)

            # 然后添加扫描任务
            for region in test_regions:
                fake_user_thread_pool.add_scan_task(region, "normal")
                fake_user_thread_pool.add_scan_task(region, "vip")

            print(f"添加了 {len(test_regions) * 2} 个任务到线程池")
            print("等待任务处理...")
            time.sleep(3)  # 增加等待时间

            print("负载均衡测试完成")

if __name__ == "__main__":
    try:
        test_thread_allocation()
        test_load_balancing()
        
        print("\n所有测试通过！✅")
        print("\n优化效果:")
        print("- 有真实用户的地区：使用独立线程，保证响应速度")
        print("- 无真实用户的地区：使用2个共享线程，减少资源消耗")
        print("- 线程池自动负载均衡，避免线程空闲")
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()
